"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Placeholder from "@/public/placeholder1.png";
import { Project } from "@prisma/client";
import ProjectTitleUpdateForm from "./project-title-update-form";
import ProjectDropdownMenu from "./project-dropdown-menu";
import Link from "next/link";

type Props = {
  project: Project;
};

const ProjectItem = ({ project: { id, title } }: Props) => {
  return (
    <li className="space-y-4 relative">
      <ProjectDropdownMenu id={id} title={title} />
      <Link href={`/project/${id}`}>
        <Card className="p-0 mb-8">
          <Image src={Placeholder} alt="project" />
        </Card>
      </Link>
      <ProjectTitleUpdateForm title={title} id={id} />
    </li>
  );
};

export default ProjectItem;

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LetterText } from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import SectionOptions from "./section-options";
import { Section } from "@prisma/client";

type Props = {
  children?: React.ReactNode;
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptionDialog = ({ children, ...rest }: Props) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add a section to your website.</DialogTitle>
        </DialogHeader>
        <SectionOptions {...rest} />
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SectionOptionDialog;

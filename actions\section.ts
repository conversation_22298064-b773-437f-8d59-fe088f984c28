"use server";

import { createSection } from "@/queries/sections";
import { ActionState, StatusCode } from "@/lib/types";
import { Section, Text } from "@prisma/client";

type CreateSectionProps = {
  projectId: Section["projectId"];
  index: Section["index"];
  type: Section["type"];
};

export const createSectionAction = async ({
  projectId,
  index,
  type,
}: CreateSectionProps): Promise<ActionState<Section>> => {
  try {
    const section = await createSection({
      projectId,
      index,
      type,
    });

    return {
      code: StatusCode.Created,
      message: "Section created successfully",
      data: section,
      success: true,
    };
  } catch (error) {
    console.error("Error creating section:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the section",
      error: error as Error,
      success: false,
    };
  }
};

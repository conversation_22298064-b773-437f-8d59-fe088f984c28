import React from "react";
import ProjectItem from "./project-item";
import { Project } from "@prisma/client";

type Props = {
  projects: Project[];
};

const ProjectsList = ({ projects }: Props) => {
  return (
    <ul className="grid grid-cols-1 gap-16 md:grid-cols-2 xl:grid-cols-3">
      {projects.map((project) => (
        <ProjectItem key={project.id} project={project} />
      ))}
    </ul>
  );
};

export default ProjectsList;

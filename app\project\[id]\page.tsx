"use server";

import React from "react";
import { auth } from "@clerk/nextjs/server";
import { notFound } from "next/navigation";
import { getProject } from "@/actions/projects";
import SectionList from "@/components/pages/project/section-list";
import ProjectHeader from "@/components/pages/project/project-header";
import ProjectFooter from "@/components/pages/project/projext-footer";

type Props = {
  params: Promise<{ id: string }>;
};

const ProjectPage = async ({ params }: Props) => {
  const { id } = await params;
  const { userId } = await auth();

  if (!userId) {
    return notFound();
  }

  const project = await getProject(id, userId);

  if (!project) {
    return notFound();
  }

  return (
    <div className="py-16">
      <ProjectHeader />

      <SectionList sections={project.sections} projectId={project.id} />

      <ProjectFooter />
    </div>
  );
};

export default ProjectPage;

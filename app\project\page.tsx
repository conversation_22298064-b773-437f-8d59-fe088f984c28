"use server";

import React from "react";
import Container from "@/components/core/container";
import ProjectHeading from "@/components/pages/projects/project-heading";
import ProjectsList from "@/components/pages/projects/projects-list";
import { auth } from "@clerk/nextjs/server";
import { getProjectOfUser } from "@/actions/projects";
import { redirect } from "next/navigation";

type Props = {};

const ProjectsPage = async (props: Props) => {
  const { userId } = await auth();

  if (!userId) {
    redirect("/");
  }

  if (!userId) {
    return (
      <Container className="space-y-10">
        <p>You are not logged in.</p>
      </Container>
    );
  }

  const projects = await getProjectOfUser(userId);

  return (
    <Container className="space-y-10">
      <ProjectHeading />
      <ProjectsList projects={projects} />
    </Container>
  );
};

export default ProjectsPage;

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LetterText } from "lucide-react";
import { createSectionAction } from "@/actions/section";
import { $Enums, Section } from "@prisma/client";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptions = ({ index, projectId }: Props) => {
  const handleSectionCreate = async () => {
    await createSectionAction({
      projectId: projectId,
      index: index,
      type: $Enums.SectionType.TEXT,
    });
  };

  return (
    <ul className="py-8 grid grid-cols-4 gap-4">
      <li className="flex justify-center items-center">
        <Button
          className="group flex flex-col items-center gap-2"
          variant="plain"
          size="none"
          onClick={handleSectionCreate}
        >
          <div className="group-hover:bg-accent p-4">
            <LetterText className="size-10" />
          </div>
          <span className="text-muted-foreground group-hover:text-foreground text-xs">
            Video & Image
          </span>
        </Button>
      </li>
    </ul>
  );
};

export default SectionOptions;

import React from "react";
import SectionButton from "./section-button";
import Container from "@/components/core/container";
import { $Enums, Project, Section, Text } from "@prisma/client";
import TextEditor from "./sections/text-section/text-editor";

type SectionWithText = Section & {
  text: Text | null;
};

type Props = {
  sections: SectionWithText[];
  projectId: Project["id"];
};

const SectionList = ({ sections, projectId }: Props) => {
  return (
    <ul>
      {sections.map(({ id, index, type, text, projectId }) => (
        <li key={id}>
          <SectionButton index={index} projectId={projectId} />

          <Container asChild>
            <section className="py-16">
              {type === $Enums.SectionType.TEXT && (
                <TextEditor text={text as Text} />
              )}
            </section>
          </Container>
        </li>
      ))}

      <li>
        <SectionButton index={sections.length} projectId={projectId} />
      </li>
    </ul>
  );
};

export default SectionList;

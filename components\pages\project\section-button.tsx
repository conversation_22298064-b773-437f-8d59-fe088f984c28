import React from "react";
import SectionOptionDialog from "./section-option-dialog";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Section } from "@prisma/client";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionButton = ({ ...rest }: Props) => {
  return (
    <div className="relative border-t hover:border-primary">
      <SectionOptionDialog {...rest}>
        <Button
          className="absolute top-0 left-1/2 -translate-y-1/2 "
          size="icon"
        >
          <Plus />
        </Button>
      </SectionOptionDialog>
    </div>
  );
};

export default SectionButton;

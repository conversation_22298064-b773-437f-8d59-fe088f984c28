"use client";

import React from "react";
import {
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { deleteProjectAction } from "@/actions/projects";
import { toast } from "sonner";
import Form from "next/form";

type Props = {
  id: string;
  children?: React.ReactNode;
};

const ProjectDeleteDialog = ({ id, children }: Props) => {
  const handleDeleteProject = async (formData: FormData) => {
    const projectId = formData.get("projectId") as string;
    const { data, message, error } = await deleteProjectAction(projectId);

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you absolutely sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete your
            account and remove your data from our servers.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="secondary">Cancel</Button>
          </DialogClose>
          <Form action={handleDeleteProject}>
            <input type="hidden" name="projectId" value={id} />
            <Button variant="destructive">Yes, Delete</Button>
          </Form>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectDeleteDialog;

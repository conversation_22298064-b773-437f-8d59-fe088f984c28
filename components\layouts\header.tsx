"use server";
import React from "react";
import Logo from "./logo";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";
import Container from "../core/container";
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";
import { currentUser } from "@clerk/nextjs/server";
import { updateExistingUserId } from "@/actions/user";

type Props = {};

const Header = async (props: Props) => {
  //we need to update existing user id to clerk id based on email using prisma

  const user = await currentUser();

  await updateExistingUserId(user);

  return (
    <header className="sticky top-0 z-50 bg-background">
      <Container className="flex justify-between items-center">
        <Logo />
        <div className="flex gap-4">
          <SignedOut>
            <SignInButton>
              <Button variant="outline">Sign In</Button>
            </SignInButton>
            <SignUpButton>
              <Button>Sign Up</Button>
            </SignUpButton>
          </SignedOut>
          <SignedIn>
            <Button asChild>
              <Link href="/project">Launch</Link>
            </Button>
            <UserButton />
          </SignedIn>
        </div>
      </Container>
    </header>
  );
};

export default Header;

import React, { ComponentProps } from "react";
import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";

type Props = ComponentProps<"div"> & {
  asChild?: boolean;
};

const Container = ({ asChild, children, className, ...props }: Props) => {
  const Comp = asChild ? Slot : "div";
  return (
    <Comp {...props} className={cn(className, "container mx-auto p-8")}>
      {children}
    </Comp>
  );
};

export default Container;

import type { Metada<PERSON> } from "next";
import "./globals.css";
import Header from "@/components/layouts/header";
import favicon from "@/public/favicon.ico";
import { Quicksand } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";

export const metadata: Metadata = {
  title: "Viber - Website Builder",
  description:
    "Generate your website without coding with Viber Web Builder - The easiest way to create a website.",
};

const quicksand = Quicksand({
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <head>
          <link rel="icon" href={favicon.src} />
        </head>
        <body className={`${quicksand.className} antialiased`}>
          <Header />
          <>{children}</>
          <Toaster richColors closeButton position="bottom-right" />
        </body>
      </html>
    </ClerkProvider>
  );
}

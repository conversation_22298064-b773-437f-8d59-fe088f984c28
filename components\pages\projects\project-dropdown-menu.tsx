import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Ellipsis, Trash2Icon } from "lucide-react";
import ProjectDeleteDialog from "./project-delete-dialog";

type Props = {
  id: string;
  title: string;
};

const ProjectDropdownMenu = ({ id, title }: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="absolute top-4 right-4">
          <Ellipsis className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-primary" align="end">
        <DropdownMenuItem asChild>
          <ProjectDeleteDialog id={id}>
            <Button className="w-full">
              <Trash2Icon className="size-4" />
              <span>Delete</span>
            </Button>
          </ProjectDeleteDialog>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProjectDropdownMenu;
